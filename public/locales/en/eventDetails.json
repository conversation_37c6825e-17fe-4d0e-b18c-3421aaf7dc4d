{"title": "Lead C@Pture Form", "detailTitle": "Lead C@Pture Form - {{name}} ({{id}})", "actions": {"create": "Create", "update": "Update", "deleteEvent": "Delete", "cancel": "Cancel", "download": "Download", "selectAll": "Select All", "deselectAll": "Deselect All", "addCustomizedField": "Add Customized Field", "actions": "Actions", "generate": "Generate", "duplicate": "Duplicate", "delete": "Delete", "addTimeSlot": "Add Time Slot", "addSpecificDates": "Add Specific Dates"}, "messages": {"createSubmitting": "Creating Lead C@Pture Form..", "createSuccessful": "Lead C@Pture Form created", "deleteSubmitting": "Requesting Lead C@Pture Form deletion..", "deleteFailed": "Failed to delete Lead C@Pture Form", "deleteSuccessful": "Lead C@Pture Form deleted", "updateSubmitting": "Updating Lead C@Pture Form..", "updateSuccessful": "Lead C@Pture Form updated", "noMoreReservation": "No more reservation.", "noMoreLead": "No more lead.", "noMoreAppointment": "No more appointment.", "password": "Password", "updateKycFieldSubmitting": "Updating Lead C@Pture Form KYC fields..", "updateKycFieldSuccessful": "Lead C@Pture Form KYC fields updated", "generatingUserSpecificUrl": "Generating User-Specific URL & QR Code..", "generateUserSpecificUrlSuccessful": "User-Specific URL & QR Code generated"}, "placeholders": {"pleaseSelect": "Please Select", "zero": "0"}, "labels": {"specificDates": "Specific Dates", "specificDatesWithRange": "Specific Dates ({{startDate}} - {{endDate}})", "noData": "No Data", "days": "days"}, "tabs": {"mainDetails": "Main Details", "customerInformation": "Customer Information", "reservations": "Reservations", "leads": "Leads", "appointments": "Appointments"}, "panelTitles": {"mainDetails": "Main Details", "utmParametersDetails": "UTM Parameters Details", "settings": "Settings", "testDriveBookingSlots": "Test Drive Booking Slots", "bannerDetails": "Banner", "vehicleAssignment": "Vehicle Assignment - {{dealerName}}", "consentsAndDeclarations": "Consents", "additionalConsents": "Additional Consents", "customizedFields": "Additional Information", "emailContents": "Email Contents", "thankYouPage": "Thank You Page Contents"}, "subSections": {"accessSettings": "Access Settings", "leadManagementSettings": "Lead Management Settings", "paymentSettings": "Payment Settings", "appointmentSettings": "Appointment Settings", "testDriveBookingSlots": "Test Drive Booking Slots", "myinfoSettings": "Myinfo Settings", "c@pIntegrationSettings": "C@P Integration Settings", "displaySettings": "Display Settings", "availableTimeSlot": "Available Time Slot"}, "fields": {"eventId": {"label": "Lead C@Pture Form ID"}, "displayName": {"label": "Internal Reference Form Name"}, "module": {"label": "<PERSON><PERSON><PERSON>"}, "eventName": {"label": "Form Title"}, "period": {"label": "Start Date/End Date"}, "startTime": {"label": "Start Time"}, "endTime": {"label": "End Time"}, "active": {"label": "Active"}, "leadOrigin": {"label": "Lead Origin"}, "medium": {"label": "Medium"}, "campaignId": {"label": "Campaign ID"}, "assignee": {"label": "Responsible Sales Consultant"}, "access": {"label": "Form Access Type"}, "scenario": {"label": "Lead C@Pture Form Request"}, "paymentModule": {"label": "Payment Module"}, "tradeIn": {"label": "Trade-In"}, "testDrive": {"label": "Test Drive"}, "myInfoSetting": {"label": "Myinfo Setting"}, "reservationInstruction": {"label": "Reservation Instruction"}, "urlSlug": {"label": "URL Slug"}, "url": {"label": "URL"}, "qrCode": {"label": "QR Code"}, "generateUserSpecificUrl": {"label": "Generate User-Specific URL & QR Code"}, "user": {"label": "User"}, "dealers": {"label": "Dealers"}, "owner": {"label": "Owner"}, "showLiveChat": {"label": "Show Live Chat"}, "showDealership": {"label": "Display Dealership"}, "characterLimit": {"label": "Character Limit"}, "order": {"label": "Order"}, "isMandatory": {"label": "Mandatory"}, "customizedFieldName": {"label": "Field name"}, "skipForDeposit": {"label": "<PERSON><PERSON>"}, "hasCustomiseBanner": {"label": "Display Banner"}, "hasCustomiseEmail": {"label": "Custom Email Contents"}, "isSearchCapCustomerOptional": {"label": "Optional to Search Customer"}, "prequalification": {"label": "Require Pre-Qualification"}, "enableDynamicUtmTracking": {"label": "Enable Dynamic UTM Tracking"}, "isCapEnabled": {"label": "Connect to Porsche C@P"}, "capLeadOrigin": {"label": "C@P Lead Origin"}, "capLeadSource": {"label": "C@P Lead Source"}, "capCampaignId": {"label": "C@P Campaign ID"}, "isPorscheIdLoginMandatory": {"label": "Porsche ID Login is Mandatory"}, "isCustomerDataRetreivalByPorscheId": {"label": "Customer Data Retrieval by Porsche ID"}, "displayAppointmentDatepicker": {"label": "Display Book a Test Drive Datepicker"}, "customTestDriveBookingSlots": {"isEnabled": {"label": "Custom Test Drive Booking slots"}}, "bookingPeriodType": {"label": "Booking Period Type"}, "startEndDate": {"label": "Start Date/End Date"}, "advancedBookingLimit": {"label": "Advanced Booking Limit"}, "unavailableDayOfWeek": {"label": "Unavailable Day Of Week"}, "maxAdvancedBookingLimit": {"label": "Max. Advance Booking"}, "timeSlot": {"label": "Time Slot {{offset}}"}, "bookingLimit": {"label": "Booking Limit"}, "displayVisitAppointmentDatepicker": {"label": "Display Book a Showroom Visit Datepicker"}, "depositAmount": {"label": "<PERSON><PERSON><PERSON><PERSON> Amou<PERSON>"}, "excludeVehicleOfInterest": {"label": "Exclude Vehicle of Interest"}, "salesConsultantAutoAssignmentEnabled": {"label": "Enable Origin Sales Consultant Auto-Assignment"}, "hasCustomiseThankYouPage": {"label": "Custom Thank You Page Contents"}, "thankYouPageIntro": {"label": "Intro Title", "placeholder": "Your application has been submitted successfully."}, "thankYouPageContent": {"label": "Content Text", "placeholder": "Our sales consultant will be in touch with you shortly."}, "useCustomRedirectButton": {"label": "Use Custom Redirect Button"}, "redirectButtonTitle": {"label": "Redirect Button Title", "placeholder": "Continue Browsing"}, "redirectUrl": {"label": "Redirect URL", "placeholder": "https://www.porsche.com/singapore/en/models/911/"}}, "application": {"assignee": "Assigned: {{assignee}}", "variant": "variant", "loanAmount": "<PERSON><PERSON>"}, "kycSections": {"kycFields": "Customer Details"}, "kycPresets": {"columns": {"key": "Field", "isRequired": "Mandatory", "isSelected": "Active", "availableOnUserInputs": "UserInput", "availableOnMyInfo": "Myinfo", "placement": "Placement"}}, "modals": {"downloadApplication": {"title": "Download Lead Applications", "description": "Please select the time frame of applications to download."}, "deleteEvent": {"title": "Deletion", "content": "Deleting this Lead C@Pture Form will disassociate {{relatedIdentifiers}} tagged to it. Do you want to proceed?", "contentEmpty": "Deleting this Lead C@Pture Form will disassociate reservations, leads, and appointments tagged to it. Do you want to proceed?", "okText": "Yes", "cancelText": "No"}}, "downloadModal": {"descriptions": {"core": "Please select the channel and the timeframe of {{type}} to download", "reservations": "$t(eventDetails:downloadModal.descriptions.core, {\"type\": \"reservations\" })", "leads": "$t(eventDetails:downloadModal.descriptions.core, {\"type\": \"leads\" })"}, "titles": {"core": "Download {{type}}", "reservations": "$t(eventDetails:downloadModal.titles.core, {\"type\": \"Reservations\" })", "leads": "$t(eventDetails:downloadModal.titles.core, {\"type\": \"Leads\" })"}, "options": {"channel": {"new": "New", "preOwned": "Pre-Owned"}, "timeframe": {"currentMonth": "Current Month", "lastMonth": "Last Month", "last3Months": "Last 3 Months", "last6Months": "Last 6 Months", "last12Months": "Last 12 Months"}}, "placeHolders": {"selectChannel": "Select Channel", "selectTimeframe": "Select Timeframe"}}, "options": {"eventAccess": {"login": "<PERSON><PERSON>", "public": "Public"}, "scenario": {"lead": "Lead C@Pture (No Payment and No Financing)", "reservation": "Payment Only", "appointment": "Appointment for Test Drive", "visitAppointment": "Appointment for Showroom Visit"}, "eventLeadOriginType": {"dealer": "Dealer", "internet": "Internet"}, "eventMediumType": {"walkin": "Walk-In", "internet": "Internet", "event": "Event"}, "bookingPeriodType": {"fixedPeriod": "Specific Dates", "bookingWindow": "Weekly Pattern"}}, "tooltips": {"assignee": "Sales consultant responsible for managing this lead.", "scenario": "Select options to specify the purpose of this form.", "isCapEnabled": "Toggle on to connect this form with Porsche C@P.", "displayName": "Internal reference name visible only to administrators. Not shown to customers.", "eventName": "Form Title visible to customers when there’s no banner.", "definedFields": "Porsche C@P defined fields", "showDealership": "Enabling this will display both the Dealership selector and it's selected Address in Lead C@Pture Form.", "owner": "The selected owner and all users in their parent user groups with permissions, can manage this Lead C@Pture Form.", "enableDynamicUtmTracking": "Enable this setting to capture UTM parameters from prior clicks (e.g., source and medium) to enable first-touchpoint attribution. These UTM details are mapped to corresponding C@P fields (Lead Origin, Lead Source, Campaign ID) configured for precise campaign tracking with case sensitivity for accurate source distinctions.", "capPrequalification": "Enables lead qualification. When disabled, leads will go directly to C@P upon form submission. It will also be automatically enabled when 'Exclude Vehicle of Interest' is turned on.", "skipForDeposit": "The customer can select \"Skip Deposit\" on the payment page to opt for offline payment.", "depositAmount": "When a deposit is required, the dealer's specified deposit amount will take priority. If no dealer-specific amount is set, the value from this field will be applied.", "utmActionField": "At least one set of UTM Parameters is required.", "useCustomRedirectButton": "When disabled, the default redirect button will be used. Enable this to customize the Redirect button title and Redirect URL.", "bookingLimit": "The maximum number of test drive bookings allowed for a specific time slot. Set to 0 for unlimited bookings.", "advancedBookingLimit": "The minimum number of day(s) in advance that a test drive must be booked. Customers won't be able to schedule a test drive earlier than this limit.", "maxAdvancedBookingLimit": "The maximum number of days in advance a customer can book a test drive. Bookings beyond this limit will not be allowed.", "vehicleAssignment": "Can't find a vehicle? It may not be assigned to this dealer. Go to the selected Dealer's Lead C@Pture Form module to assign."}, "campaignDetails": {"columnTitles": {"utmUrl": "UTM Campaign URL", "capLeadOrigin": "C@P Lead Origin", "capLeadSource": "C@P Lead Source", "capCampaignId": "C@P Campaign ID", "action": "Action"}, "actions": {"addRow": "Add Another Set of UTM Parameters"}}, "userSpecificUrl": {"description": "Choose the specific Sales Consultant you want to generate unique URLs and QR codes for."}, "download": {"initiated": "Download initiated. The file will be delivered to your email shortly.", "noRecordToDownload": "There is no record to download.", "downloadNotCompleted": "Download not completed (Code: 400). Please try again or contact support for assistance."}, "drawer": {"responsibleSalesPerson": {"info": "If left empty, the corresponding dealer will use the Responsible Sales Consultant set outside this drawer"}}}