---
applyTo: '**'
---

Provide project context and coding guidelines that AI should follow when generating code, answering questions, or reviewing changes.

- use `yarn generate:schema` to generate related files after update or create new .graphql files
- use `tsc` command to check typescript error,run the
- never use `any` or `unknown` types, use specific and strongly-typed Typescript types from the existing codebase, create new one if you dont find any
