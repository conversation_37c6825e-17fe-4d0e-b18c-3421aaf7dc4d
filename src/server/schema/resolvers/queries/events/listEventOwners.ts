import { Filter } from 'mongodb';
import { User } from '../../../../database/documents';
import getDatabaseContext from '../../../../database/getDatabaseContext';
import getEventAccessibleUserIds from '../../../../utils/eventAccessibleUserIds';
import { requiresLoggedUser } from '../../../middlewares';
import { GraphQLQueryResolvers } from '../../definitions';

const query: GraphQLQueryResolvers['listEventOwners'] = async (
    root,
    { eventId, companyId },
    { getUser, getPermissionController }
) => {
    const { collections } = await getDatabaseContext();

    const event = eventId ? await collections.events.findOne({ _id: eventId, isDeleted: false }) : null;

    const user = await getUser();

    const filter: Filter<User> = { isDeleted: false };
    const permissionController = await getPermissionController();
    if (!permissionController.hasRootPermission()) {
        const { update: accessibleUserIds } = await getEventAccessibleUserIds([user._id].filter(Boolean), companyId);
        const userIds = [
            ...(event?.ownerId ? [event.ownerId] : []),
            ...(accessibleUserIds[user._id.toHexString()] ?? []),
        ].filter(Boolean);

        filter._id = { $in: userIds };
    }

    // For root permission, replicate looking up user groups like `resolver/types/User.userGroups` does
    // But filter out by company id if exists
    if (companyId) {
        return collections.users
            .aggregate<User>([
                {
                    $match: filter,
                },
                {
                    $lookup: {
                        from: 'userGroups',
                        localField: '_id',
                        foreignField: 'userIds',
                        as: 'userGroupCheck',
                    },
                },
                {
                    $match: {
                        // Query from user groups attached, that contain companyId
                        userGroupCheck: {
                            $elemMatch: { companyId },
                        },
                    },
                },
            ])
            .toArray();
    }

    // If not, just return all
    return collections.users.find(filter).toArray();
};

export default requiresLoggedUser(query);
