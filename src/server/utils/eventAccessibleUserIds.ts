import { isNil } from 'lodash/fp';
import type { ObjectId, Document } from 'mongodb';
import type { UserGroup } from '../database/documents';
import getDatabaseContext from '../database/getDatabaseContext';
import * as oid from './oid';

type AggregatedUserGroup = UserGroup & {
    superiors?: UserGroup[] | null;
    ancestors?: UserGroup[] | null;
};

type EventAccessibleUserIds = {
    view: ObjectId[];
    update: ObjectId[];
};

const loadCompleteEventHierarchyData = async (currentUserIds: readonly ObjectId[], companyId?: ObjectId) => {
    const { collections } = await getDatabaseContext();

    // Descendant pipeline
    const descendantPipeline: Document[] = [
        {
            $graphLookup: {
                from: 'userGroups',
                startWith: '$superiorUserGroupIds',
                connectFromField: 'superiorUserGroupIds',
                connectToField: '_id',
                as: 'superiors',
                maxDepth: 100, // to prevent dead loops
            },
        },
        {
            $match: {
                'superiors.userIds': { $in: currentUserIds },
                ...(companyId ? { 'superiors.companyId': companyId } : {}),
            },
        },
    ];

    // Ancestor pipeline
    const ancestorPipeline: Document[] = [
        {
            $match: {
                userIds: { $in: currentUserIds },
                ...(companyId ? { companyId } : {}),
            },
        },
        {
            $graphLookup: {
                from: 'userGroups',
                startWith: '$superiorUserGroupIds',
                connectFromField: 'superiorUserGroupIds',
                connectToField: '_id',
                as: 'ancestors',
                maxDepth: 100,
            },
        },
    ];

    const siblings = await collections.userGroups
        .find({
            userIds: { $in: currentUserIds },
            ...(companyId ? { companyId } : {}),
        })
        .toArray();

    const [descendants, ancestorResults] = await Promise.all([
        collections.userGroups.aggregate<AggregatedUserGroup>(descendantPipeline).toArray(),
        collections.userGroups.aggregate<AggregatedUserGroup>(ancestorPipeline).toArray(),
    ]);

    const ancestors = ancestorResults.flatMap(result => result.ancestors || []);

    return { descendants, ancestors, siblings };
};

const buildEventAccessibleUserIds = (
    currentUserId: ObjectId,
    descendants: AggregatedUserGroup[],
    ancestors: UserGroup[],
    siblings: UserGroup[]
): EventAccessibleUserIds => {
    const baseUserIds = [
        currentUserId,
        ...descendants
            .filter(({ superiors }) =>
                superiors?.some(({ userIds }) => userIds.some(userId => userId.equals(currentUserId)))
            )
            .flatMap(({ userIds }) => userIds),
        ...siblings
            .filter(({ userIds }) => userIds.some(userId => userId.equals(currentUserId)))
            .flatMap(({ userIds }) => userIds),
    ];

    const viewUserIds = oid.unique([...baseUserIds, ...ancestors.flatMap(({ userIds }) => userIds)]);

    // descendant should not have access to event owned by ancestors
    const updateUserIds = oid.unique([...baseUserIds]);

    return {
        view: viewUserIds,
        update: updateUserIds,
    };
};

const getEventAccessibleUserIds = async (
    currentUserIds: readonly ObjectId[] | null | undefined,
    companyId?: ObjectId
): Promise<{
    view: Record<string, ObjectId[]>;
    update: Record<string, ObjectId[]>;
}> => {
    if (isNil(currentUserIds) || currentUserIds.length === 0) {
        return { view: {}, update: {} };
    }

    const { descendants, ancestors, siblings } = await loadCompleteEventHierarchyData(currentUserIds, companyId);

    const viewResults: Record<string, ObjectId[]> = {};
    const updateResults: Record<string, ObjectId[]> = {};

    currentUserIds.forEach(currentUserId => {
        const key = currentUserId.toHexString();
        const accessibleUserIds = buildEventAccessibleUserIds(currentUserId, descendants, ancestors, siblings);

        viewResults[key] = accessibleUserIds.view;
        updateResults[key] = accessibleUserIds.update;
    });

    return { view: viewResults, update: updateResults };
};

export default getEventAccessibleUserIds;
