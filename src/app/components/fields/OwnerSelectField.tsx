import { useFormikContext } from 'formik';
import { useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import type { UsersOptionsDataFragment } from '../../api/fragments/UsersOptionsData';
import { useGetEventOwnersQuery } from '../../api/queries';
import { useThemeComponents } from '../../themes/hooks';
import { useAccountContext } from '../contexts/AccountContextManager';
import { useCompany } from '../contexts/CompanyContextManager';
import { type SelectFieldProps } from './SelectField';

export type OwnerSelectFieldProps = Omit<SelectFieldProps, 'children' | 'options'> & {
    owners?: UsersOptionsDataFragment[];
    companyId?: string;
    eventId?: string;
    forceSkipFetch?: boolean;
    addNoneOption?: boolean;
    autoSelectCurrentUser?: boolean;
};

const OwnerSelectField = ({
    owners: ownersFromProps,
    companyId,
    eventId,
    forceSkipFetch = false,
    addNoneOption = false,
    autoSelectCurrentUser = true,
    name,
    ...props
}: OwnerSelectFieldProps) => {
    const { owners, options, loading } = useOwnerSelectFieldOptions({
        companyId,
        eventId,
        owners: ownersFromProps,
        forceSkipFetch,
        addNoneOption,
    });

    const { FormFields } = useThemeComponents();
    const { data: accountContext } = useAccountContext();
    const { values, setFieldValue } = useFormikContext();

    // Auto-select current user for create forms (when eventId is not provided)
    useEffect(() => {
        if (autoSelectCurrentUser && !eventId && !values[name] && accountContext?.id && owners.length > 0) {
            const currentUser = owners.find(owner => owner.id === accountContext.id);
            if (currentUser) {
                setFieldValue(name, currentUser.id);
            }
        }
    }, [autoSelectCurrentUser, eventId, values, name, accountContext?.id, owners, setFieldValue]);

    return (
        <FormFields.TreeSelectField {...props} loading={!owners && loading} name={name} treeData={options} showSearch />
    );
};

export default OwnerSelectField;

export type UseOwnerSelectFieldOptionsArgs = {
    companyId?: string;
    eventId?: string;
    owners?: UsersOptionsDataFragment[];
    forceSkipFetch?: boolean;
    addNoneOption?: boolean;
};

export const useOwnerSelectFieldOptions = ({
    companyId,
    eventId,
    owners: ownersFromProps,
    forceSkipFetch = false,
    addNoneOption = false,
}: UseOwnerSelectFieldOptionsArgs) => {
    const { t } = useTranslation('moduleDetails');
    const company = useCompany(true);

    const { data, loading } = useGetEventOwnersQuery({
        variables: { companyId: companyId || company?.id, eventId },
        skip: !!ownersFromProps || forceSkipFetch || (!companyId && !company?.id),
        fetchPolicy: 'cache-and-network',
    });

    const owners = useMemo(() => ownersFromProps || data?.result || [], [data?.result, ownersFromProps]);

    const options = useMemo(() => {
        // Return empty array if owners is not available yet
        if (!owners || !Array.isArray(owners)) {
            return [];
        }

        const treeData = [];

        // Add "none" option if needed
        if (addNoneOption) {
            treeData.push({
                title: t('moduleDetails:selectFields.options.none'),
                value: null,
                children: [], // TreeSelectField requires children array
            });
        }

        // Group owners by dealer
        const dealerGroups = new Map();
        const ownersWithoutDealers = [];

        owners.forEach(owner => {
            const dealerNames = owner.userGroups?.flatMap(group => group.dealers || []).filter(Boolean) || [];

            if (dealerNames.length === 0) {
                // Owner has no associated dealers
                ownersWithoutDealers.push(owner);
            } else {
                // Group by each dealer
                dealerNames.forEach(dealer => {
                    if (!dealerGroups.has(dealer.id)) {
                        dealerGroups.set(dealer.id, {
                            dealer,
                            owners: [],
                        });
                    }
                    dealerGroups.get(dealer.id).owners.push(owner);
                });
            }
        });

        // Create tree structure - follow exact same format as Settings.tsx
        dealerGroups.forEach(({ dealer, owners: dealerOwners }) => {
            if (dealerOwners.length > 0) {
                treeData.push({
                    title: dealer.displayName,
                    value: dealer.id,
                    children: dealerOwners.map(owner => ({
                        title: owner.displayName,
                        value: owner.id,
                    })),
                });
            }
        });

        // Add owners without dealers - group them under "General"
        if (ownersWithoutDealers.length > 0) {
            treeData.push({
                title: 'General',
                value: 'general',
                children: ownersWithoutDealers.map(owner => ({
                    title: owner.displayName,
                    value: owner.id,
                })),
            });
        }

        return treeData;
    }, [addNoneOption, owners, t]);

    return { owners, options, loading };
};
