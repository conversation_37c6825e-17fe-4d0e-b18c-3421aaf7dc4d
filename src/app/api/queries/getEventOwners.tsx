import type * as SchemaTypes from '../types';

import type { UsersOptionsDataFragment } from '../fragments/UsersOptionsData';
import { gql } from '@apollo/client';
import { UsersOptionsDataFragmentDoc } from '../fragments/UsersOptionsData';
import * as <PERSON> from '@apollo/client';
const defaultOptions = {} as const;
export type GetEventOwnersQueryVariables = SchemaTypes.Exact<{
  eventId?: SchemaTypes.InputMaybe<SchemaTypes.Scalars['ObjectID']['input']>;
  companyId?: SchemaTypes.InputMaybe<SchemaTypes.Scalars['ObjectID']['input']>;
}>;


export type GetEventOwnersQuery = (
  { __typename: 'Query' }
  & { result: Array<(
    { __typename: 'User' }
    & UsersOptionsDataFragment
  )> }
);


export const GetEventOwnersDocument = /*#__PURE__*/ gql`
    query getEventOwners($eventId: ObjectID, $companyId: ObjectID) {
  result: listEventOwners(eventId: $eventId, companyId: $companyId) {
    ...UsersOptionsData
  }
}
    ${UsersOptionsDataFragmentDoc}`;

/**
 * __useGetEventOwnersQuery__
 *
 * To run a query within a React component, call `useGetEventOwnersQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetEventOwnersQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetEventOwnersQuery({
 *   variables: {
 *      eventId: // value for 'eventId'
 *      companyId: // value for 'companyId'
 *   },
 * });
 */
export function useGetEventOwnersQuery(baseOptions?: Apollo.QueryHookOptions<GetEventOwnersQuery, GetEventOwnersQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<GetEventOwnersQuery, GetEventOwnersQueryVariables>(GetEventOwnersDocument, options);
      }
export function useGetEventOwnersLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<GetEventOwnersQuery, GetEventOwnersQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<GetEventOwnersQuery, GetEventOwnersQueryVariables>(GetEventOwnersDocument, options);
        }
export type GetEventOwnersQueryHookResult = ReturnType<typeof useGetEventOwnersQuery>;
export type GetEventOwnersLazyQueryHookResult = ReturnType<typeof useGetEventOwnersLazyQuery>;
export type GetEventOwnersQueryResult = Apollo.QueryResult<GetEventOwnersQuery, GetEventOwnersQueryVariables>;