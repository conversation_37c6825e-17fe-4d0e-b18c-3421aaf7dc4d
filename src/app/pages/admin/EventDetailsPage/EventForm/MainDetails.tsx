import { <PERSON><PERSON>, Col, Row } from 'antd';
import { useFormikContext } from 'formik';
import { useCallback, useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import type { UsersOptionsDataFragment } from '../../../../api/fragments/UsersOptionsData';
import { useGetEventOwnersQuery } from '../../../../api/queries';
import { ModuleRole } from '../../../../api/types';
import { useMultipleDealerIds } from '../../../../components/contexts/DealerContextManager';
import DealerSelectField from '../../../../components/fields/DealerSelectField';
import DealershipTreeSelectField from '../../../../components/fields/DealershipFields/DealershipTreeSelectField';
import ModuleSelectField from '../../../../components/fields/ModuleSelectField';
import { useThemeComponents } from '../../../../themes/hooks';
import useLeadGenTooltip from '../../../../utilities/useLeadGenTooltip';
import useSystemSwitchData from '../../../../utilities/useSystemSwitchData';
import type { EventFormValues } from './types';

export const colSpan = { lg: 8, md: 12, xs: 24 };

export type MainDetailsProps = {
    companyId?: string;
    eventId?: string;
    updated?: string;
    disabled?: boolean;
    identifier?: string;
    timeZone?: string;
    offset?: string;
};

const MainDetails = ({ eventId, companyId, updated, identifier, offset, disabled = false }: MainDetailsProps) => {
    console.log('companyId', companyId);
    const { t } = useTranslation(['eventDetails', 'common']);
    const { values, isSubmitting, setFieldValue } = useFormikContext<EventFormValues>();
    const { yesNoSwitch } = useSystemSwitchData();
    const { FormFields } = useThemeComponents();

    const displayNameTooltip = useLeadGenTooltip('displayName');
    const eventNameTooltip = useLeadGenTooltip('eventName');
    const showDealershipTooltip = useLeadGenTooltip('showDealership');
    const ownerTooltip = useLeadGenTooltip('owner');
    const { dealersFromApi } = useMultipleDealerIds();

    // Fetch owner data using the existing query
    const { data: ownerData, loading: ownerLoading } = useGetEventOwnersQuery({
        variables: { companyId, eventId },
        skip: !companyId,
        fetchPolicy: 'cache-and-network',
    });

    // Transform owner data to tree structure matching Sales Consultant format
    const ownerTreeData = useMemo(() => {
        if (!ownerData?.result) return [];

        const dealerGroups = new Map();
        const ownersWithoutDealers = [];

        ownerData.result.forEach(owner => {
            const dealerNames = owner.userGroups?.flatMap(group => group.dealers || []).filter(Boolean) || [];

            if (dealerNames.length === 0) {
                // Owner has no associated dealers
                ownersWithoutDealers.push(owner);
            } else {
                // Group by each dealer
                dealerNames.forEach(dealer => {
                    if (!dealerGroups.has(dealer.id)) {
                        dealerGroups.set(dealer.id, {
                            dealer,
                            owners: [],
                        });
                    }
                    dealerGroups.get(dealer.id).owners.push(owner);
                });
            }
        });

        const treeData = [];

        // Create tree structure - follow exact same format as Settings.tsx
        dealerGroups.forEach(({ dealer, owners: dealerOwners }) => {
            if (dealerOwners.length > 0) {
                treeData.push({
                    title: dealer.displayName,
                    value: dealer.id,
                    children: dealerOwners.map((owner: UsersOptionsDataFragment) => ({
                        title: owner.displayName,
                        value: owner.id,
                    })),
                });
            }
        });

        // Add owners without dealers - group them under "General"
        if (ownersWithoutDealers.length > 0) {
            treeData.push({
                title: 'General',
                value: 'general',
                children: ownersWithoutDealers.map((owner: UsersOptionsDataFragment) => ({
                    title: owner.displayName,
                    value: owner.id,
                })),
            });
        }

        return treeData;
    }, [ownerData?.result]);

    const showDealershipSwitch = useMemo(() => !(values.dealerIds?.length > 1), [values.dealerIds?.length]);
    useEffect(() => {
        if (values.dealerIds?.length > 1) {
            setFieldValue('showDealership', true);
        }
    }, [setFieldValue, values.dealerIds?.length]);

    // When dealer is selected, then it also preparing the initial value for the public salesperson dealership value
    const onDealersChange = useCallback(
        (dealerIds: string[]) => {
            if (!values.privateAccess) {
                if (!dealerIds.length) {
                    setFieldValue('publicSalesPerson', {
                        defaultId: values.publicSalesPerson?.defaultId,
                        overrides: [],
                    });
                } else {
                    setFieldValue('publicSalesPerson', {
                        defaultId: values.publicSalesPerson?.defaultId,
                        overrides: dealerIds.map(dealerId => {
                            const dealerValue = (values.publicSalesPerson?.overrides || []).find(
                                override => override.dealerId === dealerId
                            );

                            if (dealerValue?.relatedId) {
                                return dealerValue;
                            }

                            return { dealerId, relatedId: null };
                        }),
                    });
                }
            }
        },
        [setFieldValue, values.privateAccess, values.publicSalesPerson?.defaultId, values.publicSalesPerson?.overrides]
    );

    return (
        <Row gutter={16}>
            {identifier && (
                <Col {...colSpan}>
                    <FormFields.DisplayField
                        {...t('eventDetails:fields.eventId', { returnObjects: true })}
                        value={identifier}
                    />
                </Col>
            )}
            <Col {...colSpan}>
                <FormFields.InputField
                    {...t('eventDetails:fields.displayName', { returnObjects: true })}
                    disabled={disabled}
                    name="displayName"
                    tooltip={displayNameTooltip}
                    required
                />
            </Col>
            <Col {...colSpan}>
                <FormFields.TranslatedInputField
                    {...t('eventDetails:fields.eventName', { returnObjects: true })}
                    disabled={disabled}
                    name="name"
                    tooltip={eventNameTooltip}
                    required
                />
            </Col>
            <Col {...colSpan}>
                <ModuleSelectField
                    {...t('eventDetails:fields.module', { returnObjects: true })}
                    disabled={!!updated || disabled}
                    moduleRole={ModuleRole.EventApplication}
                    name="moduleId"
                    required
                />
            </Col>
            <Col {...colSpan}>
                <FormFields.RangePickerField
                    disabled={disabled}
                    label={t('eventDetails:fields.period.label')}
                    name="period"
                    required
                />
            </Col>
            <Col {...colSpan}>
                <FormFields.TimePickerField
                    {...t('eventDetails:fields.startTime', { returnObjects: true })}
                    disabled={disabled}
                    formTouched={isSubmitting}
                    name="startTime"
                    offset={offset}
                    required
                />
            </Col>
            <Col {...colSpan}>
                <FormFields.TimePickerField
                    {...t('eventDetails:fields.endTime', { returnObjects: true })}
                    disabled={disabled}
                    formTouched={isSubmitting}
                    name="endTime"
                    offset={offset}
                    required
                />
            </Col>

            <Col {...colSpan}>
                <DealerSelectField
                    {...t('eventDetails:fields.dealers', { returnObjects: true })}
                    companyId={companyId}
                    dealers={dealersFromApi.map(dealer => ({
                        __typename: 'Dealer',
                        displayName: dealer.displayName,
                        id: dealer.id,
                        legalName: dealer.legalName,
                    }))}
                    disabled={disabled}
                    forceSkipFetch={false}
                    mode="multiple"
                    name="dealerIds"
                    onChange={onDealersChange}
                    addSelectAll
                    isTranslatedOption
                    required
                    showSearch
                />
            </Col>

            <Col {...colSpan}>
                <DealershipTreeSelectField
                    {...t('eventDetails:fields.owner', { returnObjects: true })}
                    companyId={companyId}
                    dealers={dealersFromApi}
                    disabled={disabled}
                    info={<Alert message={t('eventDetails:drawer.owner.info')} type="info" showIcon />}
                    loading={ownerLoading}
                    name="ownerId"
                    tooltip={ownerTooltip}
                    treeData={ownerTreeData}
                    required
                    showSearch
                />
            </Col>

            {showDealershipSwitch && (
                <Col {...colSpan}>
                    <FormFields.SwitchField
                        {...yesNoSwitch}
                        {...t('eventDetails:fields.showDealership', { returnObjects: true })}
                        disabled={disabled}
                        name="showDealership"
                        tooltip={showDealershipTooltip}
                    />
                </Col>
            )}

            <Col {...colSpan}>
                <FormFields.SwitchField
                    {...t('eventDetails:fields.active', { returnObjects: true })}
                    {...yesNoSwitch}
                    disabled={disabled}
                    name="isActive"
                    required
                />
            </Col>

            {updated && (
                <Col {...colSpan}>
                    <FormFields.DisplayFieldWithUTCOffset
                        {...t('common:fields.updated', { returnObjects: true, offset })}
                        value={updated}
                    />
                </Col>
            )}
        </Row>
    );
};

export default MainDetails;
