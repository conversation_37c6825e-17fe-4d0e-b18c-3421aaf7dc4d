import { <PERSON><PERSON>, <PERSON><PERSON>, Col } from 'antd';
import { useFormikContext } from 'formik';
import { isBoolean, isEmpty, isNil } from 'lodash/fp';
import type { DefaultOptionType } from 'rc-tree-select/lib/TreeSelect';
import { useCallback, useEffect, useMemo, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import urljoin from 'url-join';
import { useGetPublicAssigneesQuery } from '../../../../api';
import { EventApplicationModuleSpecsFragment } from '../../../../api/fragments/EventApplicationModuleSpecs';
import { EventDataFragment } from '../../../../api/fragments/EventData';
import { ApplicationScenario, EventMediumType, LocalCustomerFieldKey, ModuleType } from '../../../../api/types';
import PanelSubSection from '../../../../components/PanelSubSection';
import QrCodeImage from '../../../../components/QrCodeImage';
import { useCompanyContext } from '../../../../components/contexts/CompanyContextManager';
import { useMultipleDealerIds } from '../../../../components/contexts/DealerContextManager';
import DealerDepositAmountField from '../../../../components/fields/DealershipFields/DealerDepositAmountField';
import DealershipTreeSelectField from '../../../../components/fields/DealershipFields/DealershipTreeSelectField';
import FormItem from '../../../../components/fields/FormItem';
import { useThemeComponents } from '../../../../themes/hooks';
import isValidEncodedURLParameters from '../../../../utilities/isValidEncodedURLParameters';
import useCapCampaignIdOptions from '../../../../utilities/useCapCampaignIdOptions';
import useEventOptions from '../../../../utilities/useEventOptions';
import useLeadGenTooltip from '../../../../utilities/useLeadGenTooltip';
import useMyInfoOption from '../../../../utilities/useMyInfoOption';
import useSystemSwitchData from '../../../../utilities/useSystemSwitchData';
import {
    hasAppointmentScenario,
    hasPaymentScenario,
    hasVisitAppointmentScenario,
} from '../../ModuleDetailsPage/modules/implementations/shared';
import useModuleOptions from '../../ModuleDetailsPage/modules/implementations/shared/useModuleOptions';
import { useGenerateUserSpecificUrlModal } from './GenerateUserSpecificUrlModal';
import type { EventFormValues } from './types';

const colSpan = { lg: 8, md: 12, xs: 24 };

const testDriveKYCFields = [
    LocalCustomerFieldKey.DrivingLicense,
    LocalCustomerFieldKey.DrivingLicenseMy,
    LocalCustomerFieldKey.DrivingLicenseTh,
    LocalCustomerFieldKey.UaeDrivingLicense,
    LocalCustomerFieldKey.UploadDrivingLicense,
];

const tradeInKYCFields = [
    LocalCustomerFieldKey.CurrentVehicleSource,
    LocalCustomerFieldKey.CurrentVehicleOwnership,
    LocalCustomerFieldKey.CurrentVehicleMake,
    LocalCustomerFieldKey.CurrentVehicleModel,
    LocalCustomerFieldKey.CurrentVehicleEquipmentLine,
    LocalCustomerFieldKey.CurrentVehicleModelYear,
    LocalCustomerFieldKey.CurrentVehiclePurchaseYear,
    LocalCustomerFieldKey.CurrentVehicleEngineType,
    LocalCustomerFieldKey.CurrentVehicleMileage,
    LocalCustomerFieldKey.CurrentVehicleRegistrationNumber,
    LocalCustomerFieldKey.CurrentVehicleContractEnd,
    LocalCustomerFieldKey.CurrentVehiclePotentialReplacement,
    LocalCustomerFieldKey.CurrentVehicleVin,
];

export type SettingsProps = {
    hasAppointmentModule: boolean;
    hasLiveChatSetting: boolean;
    disabled?: boolean;
    eventModule: Pick<
        Extract<EventDataFragment['module'], { __typename: 'EventApplicationModule' }>,
        'id' | 'capModuleId' | 'displayAppointmentDatepicker' | 'displayVisitAppointmentDatepicker'
    >;
    company: EventApplicationModuleSpecsFragment['company'];
    firstRouterPath?: EventDataFragment['firstRouterPath'];
    eventId?: string;
    hasVisitAppointmentModule: boolean;
};

const Settings = ({
    disabled = false,
    company,
    hasAppointmentModule,
    hasLiveChatSetting,
    firstRouterPath,
    eventModule,
    eventId,
    hasVisitAppointmentModule,
}: SettingsProps) => {
    const { t } = useTranslation('eventDetails');
    const { FormFields } = useThemeComponents();
    const prevModuleId = useRef<string | undefined>(eventModule?.id);

    const { availableCompanies } = useCompanyContext();
    const { dealersFromApi } = useMultipleDealerIds();

    const options = useModuleOptions(company, { addNoneOptions: { payment: true } });
    const { initialValues, values, setFieldValue, setValues } = useFormikContext<EventFormValues>();
    const {
        eventAccessOptions,
        eventScenarioOptions: defaultEventScenarioOptions,
        eventLeadOriginTypeOptions,
        eventMediumTypeOptions,
    } = useEventOptions();
    const { yesNoSwitch } = useSystemSwitchData();
    const { myInfoOptions, hasMyInfoModule } = useMyInfoOption();
    const capCampaignIdOptions = useCapCampaignIdOptions(company?.id);

    const assigneeTooltip = useLeadGenTooltip('assignee');
    const scenarioTooltip = useLeadGenTooltip('scenario');
    const enableDynamicUtmTrackingTooltip = useLeadGenTooltip('enableDynamicUtmTracking');
    const definedFieldsTooltip = useLeadGenTooltip('definedFields');
    const capPrequalificationTooltip = useLeadGenTooltip('capPrequalification');
    const skipForDepositTooltip = useLeadGenTooltip('skipForDeposit');
    const depositAmountTooltip = useLeadGenTooltip('depositAmount');

    // Handle scenario change
    useEffect(() => {
        if (hasAppointmentModule && hasAppointmentScenario(values.scenarios)) {
            setFieldValue('isAllowTestDrive', true);
        } else {
            setFieldValue('isAllowTestDrive', false);
        }
    }, [hasAppointmentModule, setFieldValue, values.scenarios]);

    useEffect(() => {
        if (values.enableDynamicUtmTracking && values.utmParametersSettings.overrides?.length === 0) {
            setFieldValue('utmParametersSettings.overrides', [
                ...(values.utmParametersSettings.overrides ?? []),
                {
                    capLeadSource: undefined,
                    capLeadOrigin: undefined,
                    capCampaignId: undefined,
                    utmUrl: undefined,
                },
            ]);
        } else if (!values.enableDynamicUtmTracking) {
            setFieldValue('utmParametersSettings.overrides', [
                // remove invalid records
                ...values.utmParametersSettings.overrides.filter((record, i) => {
                    if (
                        !record.utmUrl ||
                        !record.capLeadSource ||
                        !record.capLeadOrigin ||
                        (record.capLeadOrigin !== EventMediumType.Walkin && !record.capCampaignId)
                    ) {
                        return false;
                    }

                    const eventFormUrl =
                        firstRouterPath && values.urlSlug
                            ? urljoin(firstRouterPath, `/${values.urlSlug}`)
                            : firstRouterPath;

                    if (
                        record.utmUrl?.length > 2048 ||
                        !/^(http|https)/.test(record.utmUrl) ||
                        record.utmUrl.includes(' ')
                    ) {
                        return false;
                    }

                    let url = null;
                    try {
                        url = new URL(record.utmUrl);
                    } catch (error) {
                        return false;
                    }

                    const domainAndPath = url.href.split('?')[0];
                    if (!isNil(eventFormUrl) && domainAndPath !== eventFormUrl) {
                        return false;
                    }

                    let duplicatedIndex = -1;
                    const duplicateUrls = values.utmParametersSettings.overrides.some((override, currentIndex) => {
                        if (record.utmUrl === override.utmUrl && i !== currentIndex) {
                            duplicatedIndex = currentIndex;

                            return true;
                        }

                        return false;
                    });

                    // remove record if duplicate record are appeared before
                    if (duplicateUrls && duplicatedIndex < i) {
                        return false;
                    }

                    if (
                        isEmpty(url.searchParams.get('utm_campaign')) ||
                        isEmpty(url.searchParams.get('utm_medium')) ||
                        isEmpty(url.searchParams.get('utm_source'))
                    ) {
                        return false;
                    }

                    // check encoded of url parameters
                    if (!isValidEncodedURLParameters(url.search, record.utmUrl)) {
                        return false;
                    }

                    return true;
                }),
            ]);
        }
    }, [firstRouterPath, setFieldValue, values.enableDynamicUtmTracking]);

    useEffect(() => {
        // "Optional to Search Customer" must be "YES" for Unsubscribed Dealer
        if (
            eventModule.capModuleId &&
            values.isCapEnabled &&
            !values.excludeVehicleOfInterest &&
            values.privateAccess &&
            values.dealerIds?.length > 0
        ) {
            for (const dealerId of values.dealerIds) {
                const dealer = dealersFromApi.find(d => d.id === dealerId);
                if (dealer) {
                    const company = availableCompanies.find(c => c.id === dealer.companyId);
                    if (company?.allowLimitDealerFeature && dealer?.limitFeature) {
                        setFieldValue('isSearchCapCustomerOptional', true);
                        break;
                    }
                }
            }
        }
    }, [
        availableCompanies,
        dealersFromApi,
        setFieldValue,
        values.dealerIds,
        values.privateAccess,
        values.isSearchCapCustomerOptional,
        eventModule.capModuleId,
        values.isCapEnabled,
        values.excludeVehicleOfInterest,
    ]);

    const { data, loading } = useGetPublicAssigneesQuery({
        fetchPolicy: 'cache-and-network',
        variables: { eventId, companyId: company?.id },
    });

    const selectedDealerOptions = useMemo(
        () => values.dealerIds.map(dealerId => dealersFromApi.find(dealerFromApi => dealerFromApi.id === dealerId)),
        [dealersFromApi, values.dealerIds]
    );

    const salesPersonTreeData = useMemo(
        () =>
            dealersFromApi
                .map(({ id: dealerId, displayName: dealerDisplayName }) => {
                    const availableSalespersonOnDealer = (data?.result ?? []).filter(({ userGroups }) =>
                        userGroups.some(group => group.dealers.some(dealer => dealer.id === dealerId))
                    );

                    if (!availableSalespersonOnDealer.length) {
                        return null;
                    }

                    return {
                        title: dealerDisplayName,
                        value: dealerId,
                        children: availableSalespersonOnDealer.map(({ id, displayName }) => ({
                            title: displayName,
                            value: id,
                        })),
                    };
                })
                .filter(Boolean),
        [data?.result, dealersFromApi]
    );

    const eventScenarioOptions = useMemo(
        () =>
            defaultEventScenarioOptions.filter(({ value }) => {
                if (!hasAppointmentModule && value === ApplicationScenario.Appointment) {
                    return false;
                }

                if (!hasVisitAppointmentModule && value === ApplicationScenario.VisitAppointment) {
                    return false;
                }

                const hasPaymentModule = !!options?.payment && options?.payment?.some(p => p.value);
                if (!hasPaymentModule && value === ApplicationScenario.Payment) {
                    return false;
                }

                return true;
            }),
        [defaultEventScenarioOptions, hasAppointmentModule, hasVisitAppointmentModule, options?.payment]
    );

    // Handle trade in switch change
    // to control KYC preset field "Current Vehicle Fields"
    const [tradeInFieldIndex, testDriveFieldIndexes] = useMemo(() => {
        const tradeInIndex: number[] = [];
        const testDriveIndexes: number[] = [];

        if (!values.kycPresets || values.kycPresets.length === 0) {
            return [tradeInIndex, testDriveIndexes];
        }

        values.kycPresets[0].fields.forEach((field, index) => {
            if (tradeInKYCFields.includes(field.key)) {
                tradeInIndex.push(index);
            }

            if (testDriveKYCFields.includes(field.key)) {
                testDriveIndexes.push(index);
            }
        });

        return [tradeInIndex, testDriveIndexes];
    }, [values.kycPresets]);

    const onEnableCapChange = useCallback(
        (newChecked: boolean) => {
            if (!newChecked) {
                setFieldValue('isSearchCapCustomerOptional', false);
                setFieldValue('capPrequalification', false);
            }
        },
        [setFieldValue]
    );

    const onAllowTradeInChange = useCallback(
        (newChecked: boolean) => {
            if (tradeInFieldIndex.length === 0) {
                return;
            }

            if (!newChecked) {
                return;
            }

            tradeInFieldIndex.forEach(index => {
                setFieldValue(`kycPresets.0.fields.${index}.isSelected`, newChecked);
            });
        },
        [setFieldValue, tradeInFieldIndex]
    );

    // Handle test drive change
    const previousIsAllowTestDrive = useRef<boolean>(initialValues.isAllowTestDrive);
    useEffect(() => {
        if (previousIsAllowTestDrive.current === values.isAllowTestDrive) {
            return;
        }

        previousIsAllowTestDrive.current = values.isAllowTestDrive;

        if (values.isAllowTestDrive && testDriveFieldIndexes.length > 0) {
            setValues(currentValues => {
                const newValues = { ...currentValues };

                // Update all test drive fields in the new object
                // this prevents maximum update depth exceeded error
                testDriveFieldIndexes.forEach(testDriveIndex => {
                    newValues.kycPresets[0].fields[testDriveIndex] = {
                        ...newValues.kycPresets[0].fields[testDriveIndex],
                        isSelected: true,
                        isRequired: true,
                    };
                });

                return newValues;
            });
        }
    }, [values.isAllowTestDrive, setValues, testDriveFieldIndexes]);

    const isPastEvent = useMemo(
        () => (values?.endTime ? values?.endTime.isBefore(new Date()) : false),
        [values?.endTime]
    );

    const eventFormUrl = useMemo(() => {
        if (!values.urlSlug || !firstRouterPath) {
            return '';
        }

        return urljoin(firstRouterPath, `/${values.urlSlug}`);
    }, [firstRouterPath, values.urlSlug]);

    const hasPorscheIdModule = useMemo(
        () => company?.modules.some(module => module.__typename === ModuleType.PorscheIdModule),
        [company?.modules]
    );

    useEffect(() => {
        if (
            !values.privateAccess &&
            values.isCustomerDataRetreivalByPorscheId &&
            isNil(values.isPorscheIdLoginMandatory)
        ) {
            setFieldValue('isPorscheIdLoginMandatory', true);
        }

        if (values.privateAccess) {
            setFieldValue('isCustomerDataRetreivalByPorscheId', false);
        }

        if (!values.isCustomerDataRetreivalByPorscheId) {
            setFieldValue('isPorscheIdLoginMandatory', null);
        }
    }, [
        setFieldValue,
        values.isCustomerDataRetreivalByPorscheId,
        values.isPorscheIdLoginMandatory,
        values.privateAccess,
    ]);

    useEffect(() => {
        if (eventModule?.id !== prevModuleId.current) {
            const { capModuleId } = eventModule;
            setFieldValue('isCapEnabled', !!capModuleId);
            prevModuleId.current = eventModule?.id;
        }
    }, [eventModule, setFieldValue]);

    const onAccessChanged = useCallback(
        (privateAccess: boolean) => {
            setFieldValue('capPrequalification', !privateAccess);
            setFieldValue(
                'salesConsultantAutoAssignmentEnabled',
                privateAccess ? false : values?.salesConsultantAutoAssignmentEnabled
            );

            if (!privateAccess) {
                const { dealerIds } = values;

                if (!dealerIds.length) {
                    setFieldValue('publicSalesPerson', {
                        defaultId: values.publicSalesPerson?.defaultId,
                        overrides: [],
                    });
                } else {
                    setFieldValue('publicSalesPerson', {
                        defaultId: values.publicSalesPerson?.defaultId,
                        overrides: dealerIds.map(dealerId => {
                            const dealerValue = (values.publicSalesPerson?.overrides || []).find(
                                override => override.dealerId === dealerId
                            );

                            if (dealerValue?.relatedId) {
                                return dealerValue;
                            }

                            return { dealerId, relatedId: null };
                        }),
                    });
                }
            }
        },
        [setFieldValue, values]
    );

    useEffect(() => {
        if (!values.privateAccess && !values.excludeVehicleOfInterest) {
            setFieldValue('isSearchCapCustomerOptional', true);
        }
    }, [setFieldValue, values.privateAccess, values.excludeVehicleOfInterest]);

    const onExcludeVehicleOfInterestChange = useCallback(
        (excludeVehicleOfInterest: boolean) => {
            if (excludeVehicleOfInterest) {
                setFieldValue('scenarios', []);
            }
        },
        [setFieldValue]
    );

    useEffect(() => {
        if (values.excludeVehicleOfInterest && values.isCapEnabled) {
            setFieldValue('capPrequalification', true);
        }
    }, [setFieldValue, values.excludeVehicleOfInterest, values.isCapEnabled]);

    const { isAppointmentSwitchShown, isVisitAppointmentSwitchShown, isCustomTestDriveBookingSlotsShown } =
        useMemo(() => {
            const isAppointmentSwitchShown =
                eventModule.displayAppointmentDatepicker &&
                hasAppointmentModule &&
                hasAppointmentScenario(values.scenarios);

            const isVisitAppointmentSwitchShown =
                hasVisitAppointmentModule &&
                eventModule.displayVisitAppointmentDatepicker &&
                hasVisitAppointmentScenario(values.scenarios);

            const isCustomTestDriveBookingSlotsShown = isAppointmentSwitchShown && values.displayAppointmentDatepicker;

            return {
                isAppointmentSwitchShown,
                isVisitAppointmentSwitchShown,
                isCustomTestDriveBookingSlotsShown,
            };
        }, [
            eventModule.displayAppointmentDatepicker,
            eventModule.displayVisitAppointmentDatepicker,
            hasAppointmentModule,
            hasVisitAppointmentModule,
            values.scenarios,
            values.displayAppointmentDatepicker,
        ]);

    const generateUserSpecificUrlModal = useGenerateUserSpecificUrlModal(eventId, salesPersonTreeData, eventFormUrl);

    const onOpenGenerateUrlModelClick = useCallback(() => {
        if (!eventFormUrl) {
            return;
        }

        generateUserSpecificUrlModal.open();
    }, [eventFormUrl, generateUserSpecificUrlModal]);

    const onDisplayAppointmentDatepickerChange = useCallback(
        (checked: boolean) => {
            if (!checked) {
                setFieldValue('customTestDriveBookingSlots', undefined);
            } else if (!values.customTestDriveBookingSlots) {
                setFieldValue('customTestDriveBookingSlots', {
                    isEnabled: false,
                    bookingPeriodType: undefined,
                    fixedPeriods: [],
                    bookingWindowSettings: undefined,
                });
            }
        },
        [setFieldValue, values.customTestDriveBookingSlots]
    );

    const onCustomTestDriveBookingSlotsChange = useCallback(
        (checked: boolean) => {
            if (!checked) {
                setFieldValue('customTestDriveBookingSlots', {
                    isEnabled: false,
                    bookingPeriodType: undefined,
                    fixedPeriods: [],
                    bookingWindowSettings: undefined,
                });
            } else {
                setFieldValue('customTestDriveBookingSlots', {
                    isEnabled: true,
                    bookingPeriodType: undefined,
                    fixedPeriods: [],
                    bookingWindowSettings: undefined,
                });
            }
        },
        [setFieldValue]
    );

    const filterTreeNode = useCallback(
        (input: string, treeNode: DefaultOptionType) =>
            String(treeNode.title ?? '')
                .toLowerCase()
                .includes((input ?? '').toLowerCase()),
        []
    );
    console.log('values: ', values);

    return (
        <>
            <PanelSubSection title={t('eventDetails:subSections.accessSettings')}>
                <Col {...colSpan}>
                    <FormFields.SelectField
                        {...t('eventDetails:fields.access', { returnObjects: true })}
                        disabled={disabled}
                        name="privateAccess"
                        onChange={onAccessChanged}
                        options={eventAccessOptions}
                        required
                        showSearch
                    />
                </Col>
            </PanelSubSection>

            <PanelSubSection title={t('eventDetails:subSections.leadManagementSettings')}>
                {isBoolean(values.privateAccess) && !values.privateAccess && (
                    <Col {...colSpan}>
                        <DealershipTreeSelectField
                            {...t('eventDetails:fields.assignee', { returnObjects: true })}
                            companyId={company?.id}
                            dealers={selectedDealerOptions}
                            disabled={disabled}
                            filterTreeNode={filterTreeNode}
                            info={
                                <Alert
                                    message={t('eventDetails:drawer.responsibleSalesPerson.info')}
                                    type="info"
                                    showIcon
                                />
                            }
                            loading={loading}
                            name="publicSalesPerson"
                            tooltip={assigneeTooltip}
                            treeData={salesPersonTreeData}
                            required
                            showSearch
                        />
                    </Col>
                )}

                {isBoolean(values.excludeVehicleOfInterest) && !values.excludeVehicleOfInterest && (
                    <Col {...colSpan}>
                        <FormFields.SelectField
                            {...t('eventDetails:fields.scenario', { returnObjects: true })}
                            autoSelected={false}
                            disabled={disabled}
                            mode="multiple"
                            name="scenarios"
                            options={eventScenarioOptions}
                            tooltip={scenarioTooltip}
                            showSearch
                        />
                    </Col>
                )}

                <Col {...colSpan}>
                    <FormFields.SwitchField
                        {...t('eventDetails:fields.tradeIn', { returnObjects: true })}
                        {...yesNoSwitch}
                        defaultChecked={initialValues.isAllowTradeIn}
                        disabled={disabled}
                        name="isAllowTradeIn"
                        onChange={onAllowTradeInChange}
                    />
                </Col>

                <Col {...colSpan}>
                    <FormFields.SwitchField
                        {...t('eventDetails:fields.excludeVehicleOfInterest', { returnObjects: true })}
                        {...yesNoSwitch}
                        disabled={disabled}
                        name="excludeVehicleOfInterest"
                        onChange={onExcludeVehicleOfInterestChange}
                    />
                </Col>

                {isBoolean(values.privateAccess) && !values.privateAccess && (
                    <Col {...colSpan}>
                        <FormFields.SwitchField
                            {...t('eventDetails:fields.salesConsultantAutoAssignmentEnabled', { returnObjects: true })}
                            {...yesNoSwitch}
                            disabled={disabled}
                            name="salesConsultantAutoAssignmentEnabled"
                        />
                    </Col>
                )}
            </PanelSubSection>

            {hasPaymentScenario(values.scenarios) && (
                <PanelSubSection title={t('eventDetails:subSections.paymentSettings')}>
                    <Col {...colSpan}>
                        <FormFields.DealershipCascaderField
                            {...t('eventDetails:fields.paymentModule', { returnObjects: true })}
                            disabled={disabled}
                            name="paymentSetting"
                            options={options?.payment || []}
                            required
                            showSearch
                        />
                    </Col>
                    <Col {...colSpan}>
                        <FormFields.SwitchField
                            {...yesNoSwitch}
                            {...t('eventDetails:fields.skipForDeposit', { returnObjects: true })}
                            disabled={disabled}
                            name="skipForDeposit"
                            tooltip={skipForDepositTooltip}
                        />
                    </Col>
                    <Col {...colSpan}>
                        <DealerDepositAmountField
                            {...t('eventDetails:fields.depositAmount', { returnObjects: true })}
                            name="depositAmount"
                            tooltip={depositAmountTooltip}
                        />
                    </Col>
                </PanelSubSection>
            )}

            {(isAppointmentSwitchShown || isVisitAppointmentSwitchShown) && (
                <PanelSubSection title={t('eventDetails:subSections.appointmentSettings')}>
                    {isAppointmentSwitchShown && (
                        <Col {...colSpan}>
                            <FormFields.SwitchField
                                {...yesNoSwitch}
                                {...t('eventDetails:fields.displayAppointmentDatepicker', { returnObjects: true })}
                                disabled={disabled}
                                name="displayAppointmentDatepicker"
                                onChange={onDisplayAppointmentDatepickerChange}
                            />
                        </Col>
                    )}

                    {isCustomTestDriveBookingSlotsShown && (
                        <Col {...colSpan}>
                            <FormFields.SwitchField
                                {...yesNoSwitch}
                                {...t('eventDetails:fields.customTestDriveBookingSlots.isEnabled', {
                                    returnObjects: true,
                                })}
                                defaultChecked={false}
                                disabled={disabled}
                                name="customTestDriveBookingSlots.isEnabled"
                                onChange={onCustomTestDriveBookingSlotsChange}
                            />
                        </Col>
                    )}

                    {isVisitAppointmentSwitchShown && (
                        <Col {...colSpan}>
                            <FormFields.SwitchField
                                {...yesNoSwitch}
                                {...t('eventDetails:fields.displayVisitAppointmentDatepicker', { returnObjects: true })}
                                disabled={disabled}
                                name="displayVisitAppointmentDatepicker"
                            />
                        </Col>
                    )}
                </PanelSubSection>
            )}

            {hasMyInfoModule && (
                <PanelSubSection title={t('eventDetails:subSections.myinfoSettings')}>
                    <Col {...colSpan}>
                        <FormFields.DealershipCascaderField
                            {...t('eventDetails:fields.myInfoSetting', { returnObjects: true })}
                            disabled={disabled}
                            name="myInfoSetting"
                            options={myInfoOptions}
                            required
                            showSearch
                        />
                    </Col>
                </PanelSubSection>
            )}

            <PanelSubSection title={t('eventDetails:subSections.c@pIntegrationSettings')}>
                {hasLiveChatSetting && (
                    <Col {...colSpan}>
                        <FormFields.SwitchField
                            {...t('eventDetails:fields.showLiveChat', { returnObjects: true })}
                            {...yesNoSwitch}
                            defaultChecked={initialValues.showLiveChat}
                            disabled={disabled}
                            name="showLiveChat"
                        />
                    </Col>
                )}

                {eventModule.capModuleId && (
                    <Col md={8} xs={24}>
                        <FormFields.SwitchField
                            {...t('eventDetails:fields.isCapEnabled', { returnObjects: true })}
                            {...yesNoSwitch}
                            disabled={disabled}
                            name="isCapEnabled"
                            onChange={onEnableCapChange}
                        />
                    </Col>
                )}
                <Col {...colSpan}>
                    <FormFields.SelectField
                        {...t('eventDetails:fields.capLeadOrigin', { returnObjects: true })}
                        disabled={disabled}
                        name="utmParametersSettings.defaultValue.capLeadOrigin"
                        options={eventMediumTypeOptions}
                        tooltip={definedFieldsTooltip}
                        required
                        showSearch
                    />
                </Col>
                <Col {...colSpan}>
                    <FormFields.SelectField
                        {...t('eventDetails:fields.capLeadSource', { returnObjects: true })}
                        disabled={disabled}
                        name="utmParametersSettings.defaultValue.capLeadSource"
                        options={eventLeadOriginTypeOptions}
                        tooltip={definedFieldsTooltip}
                        required
                        showSearch
                    />
                </Col>
                <Col {...colSpan}>
                    <FormFields.SelectField
                        {...t('eventDetails:fields.capCampaignId', { returnObjects: true })}
                        autoSelected={false}
                        disabled={disabled}
                        name="utmParametersSettings.defaultValue.capCampaignId"
                        options={capCampaignIdOptions}
                        required={values.utmParametersSettings.defaultValue.capLeadOrigin !== EventMediumType.Walkin}
                        allowClear
                        showSearch
                    />
                </Col>
                {eventModule.capModuleId && values.isCapEnabled && (
                    <>
                        {values.privateAccess && (
                            <Col md={8} xs={24}>
                                <FormFields.SwitchField
                                    {...yesNoSwitch}
                                    {...t('eventDetails:fields.isSearchCapCustomerOptional', {
                                        returnObjects: true,
                                    })}
                                    disabled={disabled}
                                    name="isSearchCapCustomerOptional"
                                />
                            </Col>
                        )}

                        <Col md={8} xs={24}>
                            <FormFields.SwitchField
                                {...yesNoSwitch}
                                {...t('eventDetails:fields.prequalification', { returnObjects: true })}
                                disabled={values.excludeVehicleOfInterest || disabled}
                                name="capPrequalification"
                                tooltip={capPrequalificationTooltip}
                            />
                        </Col>
                    </>
                )}
                <Col md={8} xs={24}>
                    <FormFields.SwitchField
                        {...yesNoSwitch}
                        {...t('eventDetails:fields.enableDynamicUtmTracking', { returnObjects: true })}
                        disabled={disabled}
                        name="enableDynamicUtmTracking"
                        tooltip={enableDynamicUtmTrackingTooltip}
                    />
                </Col>
                {hasPorscheIdModule && !values.privateAccess && (
                    <>
                        <Col md={8} xs={24}>
                            <FormFields.SwitchField
                                {...t('eventDetails:fields.isCustomerDataRetreivalByPorscheId', {
                                    returnObjects: true,
                                })}
                                {...yesNoSwitch}
                                disabled={disabled}
                                name="isCustomerDataRetreivalByPorscheId"
                            />
                        </Col>
                        {values.isCustomerDataRetreivalByPorscheId && (
                            <Col md={8} xs={24}>
                                <FormFields.SwitchField
                                    {...t('eventDetails:fields.isPorscheIdLoginMandatory', {
                                        returnObjects: true,
                                    })}
                                    {...yesNoSwitch}
                                    disabled={disabled}
                                    name="isPorscheIdLoginMandatory"
                                />
                            </Col>
                        )}
                    </>
                )}
            </PanelSubSection>

            <PanelSubSection title={t('eventDetails:subSections.displaySettings')} lastSection>
                <Col {...colSpan}>
                    <FormFields.SwitchField
                        {...t('eventDetails:fields.hasCustomiseBanner', {
                            returnObjects: true,
                        })}
                        {...yesNoSwitch}
                        defaultChecked={false}
                        disabled={disabled}
                        name="hasCustomiseBanner"
                    />
                </Col>
                <Col {...colSpan}>
                    <FormFields.SwitchField
                        {...t('eventDetails:fields.hasCustomiseEmail', {
                            returnObjects: true,
                        })}
                        {...yesNoSwitch}
                        defaultChecked={false}
                        disabled={disabled}
                        name="hasCustomiseEmail"
                    />
                </Col>
                <Col {...colSpan}>
                    <FormFields.SwitchField
                        {...t('eventDetails:fields.hasCustomiseThankYouPage', {
                            returnObjects: true,
                        })}
                        {...yesNoSwitch}
                        defaultChecked={false}
                        disabled={disabled}
                        name="hasCustomiseThankYouPage"
                    />
                </Col>
                {isBoolean(values.privateAccess) && !values.privateAccess && (
                    <Col {...colSpan}>
                        <FormItem {...t('eventDetails:fields.generateUserSpecificUrl', { returnObjects: true })}>
                            <Button disabled={disabled} onClick={onOpenGenerateUrlModelClick} type="primary">
                                {t('eventDetails:actions.generate')}
                            </Button>
                        </FormItem>
                    </Col>
                )}

                <Col {...colSpan}>
                    <FormFields.InputField
                        {...t('eventDetails:fields.urlSlug', { returnObjects: true })}
                        disabled={disabled}
                        name="urlSlug"
                        required
                    />
                </Col>
                <Col {...colSpan}>
                    <FormFields.DisplayField
                        {...t('eventDetails:fields.url', { returnObjects: true })}
                        isPastEvent={isPastEvent}
                        url={eventFormUrl}
                        value={eventFormUrl}
                        disabled
                    />
                </Col>
                <Col {...colSpan}>
                    <FormItem {...t('eventDetails:fields.qrCode', { returnObjects: true })}>
                        <QrCodeImage qrCodeOptions={{ width: 1000, margin: 1 }} url={eventFormUrl} width={150} />
                    </FormItem>
                </Col>
            </PanelSubSection>

            {generateUserSpecificUrlModal.render()}
        </>
    );
};

export default Settings;
