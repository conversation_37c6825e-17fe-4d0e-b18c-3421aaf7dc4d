import { useFormikContext } from 'formik';
import { useEffect, useMemo, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import * as permissionKind from '../../../../../shared/permissions';
import type { EventApplicationModuleSpecsFragment } from '../../../../api/fragments/EventApplicationModuleSpecs';
import { useGetFirstEventRouterPathQuery } from '../../../../api/queries/getFirstEventRouterPath';
import { useGetModuleSpecsQuery } from '../../../../api/queries/getModuleSpecs';
import { ContentRefinementSourceAction, ContentRefinementSourceKind, ModuleType } from '../../../../api/types';
import FormAutoTouch from '../../../../components/FormAutoTouch';
import { useAccountContext } from '../../../../components/contexts/AccountContextManager';
import GlobalStateContextProvider from '../../../../components/contexts/GenericStateOnPageContextManager';
import CollapsibleWrapper from '../../../../components/wrappers/CollapsibleWrapper';
import hasPermissions from '../../../../utilities/hasPermissions';
import { PageType } from '../../../shared/EventList/RenderShareUrl';
import BannerFormItem from '../../BannerDetailsPage/BannerFormItem';
import { useKycFieldDefinitionsFromCountry } from '../../UpdateKYCPresetPage/KYCFieldDefinitions';
import { getInitialEventKycField } from '../EventKycFields/utils';
import CampaignDetails from './CampaignDetails';
import CustomThankYouPageForm from './CustomThankYouPageForm';
import EventEmailContentsForm from './EventEmailContentsForm';
import MainDetails from './MainDetails';
import Settings from './Settings';
import TestDriveBookingSlots from './TestDriveBookingSlots';
import VehicleAssignments from './VehicleAssignments';
import { EventInnerStyledPanel } from './shared';
import type { CreateEventFormValues } from './types';

const pageType: PageType = 'Admin';

type Props = {
    onCompanyUpdate: (company: EventApplicationModuleSpecsFragment['company']) => void;
    onFirstEventRouterPathChange: (path: string) => void;
    hasEventToBeDuplicated?: boolean;
};

const CreateEventForm = ({ onCompanyUpdate, onFirstEventRouterPathChange, hasEventToBeDuplicated = false }: Props) => {
    // added more translations: 'bannerDetails', 'common', 'eventApplicationModuleDetails'
    // to fix issue the page flicker on first toggle on hasCustomiseBanner and hasCustomiseEmail
    const { t } = useTranslation(['eventDetails', 'bannerDetails', 'common', 'eventApplicationModuleDetails']);

    const { data: accountContext } = useAccountContext();
    const { values, setFieldValue } = useFormikContext<CreateEventFormValues>();
    const setFieldValueRef = useRef(setFieldValue);

    const { data: moduleData } = useGetModuleSpecsQuery({
        fetchPolicy: 'cache-and-network',
        variables: {
            moduleId: values?.moduleId,
        },
        skip: !values?.moduleId,
    });

    const definitions = useKycFieldDefinitionsFromCountry(moduleData?.module?.company?.countryCode);

    const { data: firstEventRouterPath } = useGetFirstEventRouterPathQuery({
        fetchPolicy: 'cache-and-network',
        variables: { moduleId: values?.moduleId },
        skip: !values?.moduleId,
    });

    const company = useMemo(() => {
        if (moduleData?.module?.__typename === 'EventApplicationModule') {
            return moduleData.module.company;
        }

        return null;
    }, [moduleData]);
    console.log('moduleData: ', moduleData);

    const refineContentParams = useMemo(
        () => ({
            hasPermissionToRefineContent: hasPermissions(accountContext.permissions, [permissionKind.createEvent]),
            source: {
                kind: ContentRefinementSourceKind.Event,
                action: ContentRefinementSourceAction.Add,
            },
        }),
        [accountContext.permissions]
    );

    useEffect(() => {
        if (onCompanyUpdate) {
            onCompanyUpdate(company);
        }
    }, [onCompanyUpdate, company]);

    const eventModule = useMemo(
        () => moduleData?.module?.__typename === 'EventApplicationModule' && moduleData?.module,
        [moduleData?.module]
    );
    const appointmentModule = useMemo(
        () =>
            eventModule?.appointmentModule?.__typename === 'AppointmentModule' ? eventModule.appointmentModule : null,
        [eventModule.appointmentModule]
    );
    const prevEventModuleId = useRef(eventModule?.id);

    useEffect(() => {
        if (prevEventModuleId.current !== eventModule?.id && !hasEventToBeDuplicated) {
            prevEventModuleId.current = eventModule?.id;
            const kycFields =
                eventModule?.__typename === 'EventApplicationModule' &&
                eventModule?.customerModule?.__typename === 'LocalCustomerManagementModule'
                    ? eventModule.customerModule.kycFields.slice().sort((a, b) => a.order - b.order)
                    : null;

            if (setFieldValueRef.current) {
                setFieldValueRef.current(
                    'kycPresets',
                    getInitialEventKycField(
                        definitions,
                        {
                            kycPresets: [],
                        },
                        kycFields
                    )
                );
            }
        }
    }, [eventModule, definitions, hasEventToBeDuplicated]);

    const hasAppointmentModule = useMemo(() => !!appointmentModule, [appointmentModule]);

    const hasVisitAppointmentModule = useMemo(() => !!eventModule?.visitAppointmentModule, [eventModule]);

    const hasLiveChatSetting = useMemo(() => !!eventModule?.liveChatSetting, [eventModule]);

    useEffect(() => {
        if (onFirstEventRouterPathChange) {
            onFirstEventRouterPathChange(firstEventRouterPath?.path);
        }
    }, [onFirstEventRouterPathChange, firstEventRouterPath?.path]);
    console.log('root company: ', company);

    return (
        <>
            <FormAutoTouch />
            <GlobalStateContextProvider refineContent={refineContentParams}>
                <CollapsibleWrapper
                    defaultActiveKey={[
                        'mainDetails',
                        'utmParametersDetails',
                        'settings',
                        'consentsAndDeclarations',
                        'testDriveBookingSlots',
                        'emailContents',
                        'bannerDetails',
                    ]}
                    useTransparentBackground
                >
                    <EventInnerStyledPanel
                        key="mainDetails"
                        $pageType={pageType}
                        header={t('eventDetails:panelTitles.mainDetails')}
                    >
                        <MainDetails companyId={company?.id} timeZone={company?.timeZone} />
                    </EventInnerStyledPanel>

                    <EventInnerStyledPanel
                        key="settings"
                        $pageType={pageType}
                        header={t('eventDetails:panelTitles.settings')}
                    >
                        <Settings
                            company={company}
                            eventModule={eventModule}
                            firstRouterPath={firstEventRouterPath?.path}
                            hasAppointmentModule={hasAppointmentModule}
                            hasLiveChatSetting={hasLiveChatSetting}
                            hasVisitAppointmentModule={hasVisitAppointmentModule}
                        />
                    </EventInnerStyledPanel>

                    {values.customTestDriveBookingSlots?.isEnabled && (
                        <EventInnerStyledPanel
                            key="testDriveBookingSlots"
                            $pageType={pageType}
                            header={t('eventDetails:panelTitles.testDriveBookingSlots')}
                        >
                            <TestDriveBookingSlots pageType={pageType} timeZone={company?.timeZone} />
                        </EventInnerStyledPanel>
                    )}

                    {values.enableDynamicUtmTracking && (
                        <EventInnerStyledPanel
                            key="utmParametersDetails"
                            $pageType={pageType}
                            className="added-bottom-padding"
                            header={t('eventDetails:panelTitles.utmParametersDetails')}
                        >
                            <CampaignDetails companyId={eventModule?.company?.id} />
                        </EventInnerStyledPanel>
                    )}

                    {values.hasCustomiseBanner && (
                        <EventInnerStyledPanel
                            key="bannerDetails"
                            $pageType={pageType}
                            header={t('eventDetails:panelTitles.bannerDetails')}
                        >
                            <BannerFormItem
                                companyId={eventModule?.company?.id}
                                prefix="banner"
                                type={ModuleType.EventApplicationModule}
                            />
                        </EventInnerStyledPanel>
                    )}
                    {values.hasCustomiseEmail && (
                        <EventInnerStyledPanel
                            key="emailContents"
                            $pageType={pageType}
                            header={t('eventDetails:panelTitles.emailContents')}
                        >
                            <EventEmailContentsForm
                                appointmentModule={appointmentModule}
                                dealerIds={values.dealerIds}
                                defaultValueDisabled={false}
                                prefix="eventLevelEmailSettings"
                            />
                        </EventInnerStyledPanel>
                    )}

                    {values.hasCustomiseThankYouPage && (
                        <EventInnerStyledPanel
                            key="thankYouPage"
                            $pageType={pageType}
                            header={t('eventDetails:panelTitles.thankYouPage')}
                        >
                            <CustomThankYouPageForm
                                companyId={eventModule?.company?.id}
                                dealerIds={values.dealerIds}
                                prefix="thankYouPageContent"
                            />
                        </EventInnerStyledPanel>
                    )}

                    <VehicleAssignments eventModule={eventModule} pageType="Admin" />
                </CollapsibleWrapper>
            </GlobalStateContextProvider>
        </>
    );
};

export default CreateEventForm;
