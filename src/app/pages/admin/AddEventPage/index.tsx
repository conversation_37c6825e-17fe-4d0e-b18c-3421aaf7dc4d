import { useApolloClient } from '@apollo/client';
import { message } from 'antd';
import dayjs from 'dayjs';
import { Formik } from 'formik';
import { omit } from 'lodash/fp';
import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation, useNavigate } from 'react-router-dom';
import type { EventApplicationModuleSpecsFragment } from '../../../api/fragments';
import {
    CreateEventDocument,
    type CreateEventMutation,
    type CreateEventMutationVariables,
} from '../../../api/mutations/createEvent';
import {
    AgeCalculationMethod,
    AspectRatio,
    BannerTextPosition,
    BookingPeriodType,
    EmailContentUpdateType,
    type CustomizedFieldInput,
} from '../../../api/types';
import FormAutoTouch from '../../../components/FormAutoTouch';
import ScrollToTop from '../../../components/ScrollToTop';
import useLoadingButton from '../../../components/button/useLoadingButton';
import Form from '../../../components/fields/Form';
import ConsolePageWithHeader from '../../../layouts/ConsoleLayout/ConsolePageWithHeader';
import { capitalizeFirstChar } from '../../../utilities/fp';
import useHandleError from '../../../utilities/useHandleError';
import useMyInfoOption from '../../../utilities/useMyInfoOption';
import useUploadBannerImage from '../BannerDetailsPage/useUploadBannerImage';
import EventCustomerInfoTab from '../EventDetailsPage/EventCustomerInfoTab';
import EventDetailsTabs from '../EventDetailsPage/EventDetailsTabs';
import CreateEventForm from '../EventDetailsPage/EventForm/CreateEventForm';
// eslint-disable-next-line max-len
import { transformCustomTestDriveBookingSlotsForCreate } from '../EventDetailsPage/EventForm/TestDriveBookingSlots/utils/transformTestDriveBookingSlots';
import { defaultDealershipSetting, useCreateEventFormValidator } from '../EventDetailsPage/EventForm/shared';
import type { CreateEventFormValues } from '../EventDetailsPage/EventForm/types';
import { prepareSubmissionEventKycField } from '../EventDetailsPage/EventKycFields/utils';
import Tab from '../EventDetailsPage/shared';
import { useDuplicateEventInitialValues, useHandleEventLevelAssets } from '../EventDetailsPage/utils';
import type { AppointmentEmailFormValues } from '../ModuleDetailsPage/AppointmentModulePage/shared';
import { hasPaymentScenario } from '../ModuleDetailsPage/modules/implementations/shared';
import {
    getDefaultDealershipPaymentSettingValue,
    processDealershipPaymentSettingInput,
} from '../ModuleDetailsPage/modules/implementations/shared/getDealershipPaymentSetting';

const AddEventPage = () => {
    const { t } = useTranslation(['eventDetails', 'emails']);
    const navigate = useNavigate();
    const location = useLocation();
    const { hasMyInfoModule } = useMyInfoOption();
    const [firstEventRouterPath, setFirstEventRouterPath] = useState<string>(null);

    const validate = useCreateEventFormValidator(firstEventRouterPath, hasMyInfoModule);
    const { setLoading, LoadingButton } = useLoadingButton();
    const apolloClient = useApolloClient();
    const handleEventLevelAssets = useHandleEventLevelAssets();
    const handleBannerAsset = useUploadBannerImage();

    const [currentTab, setCurrentTab] = useState<Tab>(Tab.MainDetails);
    const [company, setCompany] = useState<EventApplicationModuleSpecsFragment['company']>();

    const duplicatedEventData = location?.state?.eventDataToBeDuplicated;
    const formattedDuplicatedEventData = useDuplicateEventInitialValues(duplicatedEventData);

    const initialValues = useMemo((): CreateEventFormValues => {
        if (formattedDuplicatedEventData) {
            return formattedDuplicatedEventData;
        }

        const getTestDriveCustomerEmailInitialContents = (key: keyof AppointmentEmailFormValues['customer']) => ({
            subject: {
                defaultValue: {
                    defaultValue: t(`emails:appointment.customer${capitalizeFirstChar(key)}.subject`),
                    overrides: [],
                },
                overrides: [],
            },
            introTitle: {
                defaultValue: {
                    defaultValue: t(`emails:appointment.customer${capitalizeFirstChar(key)}.introTitle`),
                    overrides: [],
                },
                overrides: [],
            },
            contentText: {
                defaultValue: {
                    defaultValue: t(`emails:appointment.customer${capitalizeFirstChar(key)}.contentText`),
                    overrides: [],
                },
                overrides: [],
            },
            isSummaryVehicleVisible: { defaultValue: true, overrides: [] },
        });

        return {
            displayName: '',
            name: {
                defaultValue: '',
                overrides: [],
            },
            isActive: true,
            period: undefined,
            privateAccess: true,
            isAllowTestDrive: false,
            isAllowTradeIn: false,
            moduleId: undefined,
            scenarios: [],
            paymentSetting: getDefaultDealershipPaymentSettingValue(),
            skipForDeposit: false,
            myInfoSetting: defaultDealershipSetting,
            publicSalesPerson: defaultDealershipSetting,
            urlSlug: '',
            dealerIds: [],
            dealerVehicles: [],
            dealerVariantsMapping: {},
            showLiveChat: false,
            customizedFields: [],
            kycPresets: [],
            showDealership: true,
            hasCustomiseEmail: false,
            displayAppointmentDatepicker: false,
            displayVisitAppointmentDatepicker: false,
            salesConsultantAutoAssignmentEnabled: false,
            eventLevelEmailSettings: {
                submitOrder: {
                    subject: {
                        defaultValue: {
                            defaultValue: t('emails:event.submitOrder.subject'),
                            overrides: [],
                        },
                        overrides: [],
                    },
                    introTitle: {
                        defaultValue: {
                            defaultValue: t('emails:event.submitOrder.introTitle'),
                            overrides: [],
                        },
                        overrides: [],
                    },
                    contentText: {
                        defaultValue: {
                            defaultValue: t('emails:event.submitOrder.contentText'),
                            overrides: [],
                        },
                        overrides: [],
                    },
                },
                testDrive: {
                    customer: {
                        submitConfirmation: getTestDriveCustomerEmailInitialContents('submitConfirmation'),
                        endTestDriveWithProcess: getTestDriveCustomerEmailInitialContents('endTestDriveWithProcess'),
                        completeTestDriveWithoutProcess: getTestDriveCustomerEmailInitialContents(
                            'completeTestDriveWithoutProcess'
                        ),
                        bookingAmendment: getTestDriveCustomerEmailInitialContents('bookingAmendment'),
                        bookingConfirmation: getTestDriveCustomerEmailInitialContents('bookingConfirmation'),
                        bookingCancellation: getTestDriveCustomerEmailInitialContents('bookingCancellation'),
                    },
                },
            },
            enableDynamicUtmTracking: false,
            utmParametersSettings: {
                defaultValue: {
                    capLeadSource: undefined,
                    capLeadOrigin: undefined,
                    capCampaignId: undefined,
                },
                overrides: [],
            },
            banner: {
                bannerHeader: {
                    defaultValue: '',
                    overrides: [],
                },
                bannerText: {
                    defaultValue: '',
                    overrides: [],
                },
                isActive: true,
                bannerTextPosition: BannerTextPosition.Left,
                mobileRatio: AspectRatio.Square,
            },
            depositAmount: {
                defaultValue: 0,
                overrides: [],
            },
            kycExtraSettings: {
                minimumAge: 18,
                ageCalculationMethod: AgeCalculationMethod.BirthdayBased,
                mobileVerification: false,
            },
            excludeVehicleOfInterest: false,
            isCapEnabled: false,
            startTime: dayjs('00:00:00', 'HH:mm:ss'),
            endTime: dayjs('23:59:00', 'HH:mm:ss'),
            hasCustomiseThankYouPage: false,
            thankYouPageContent: {
                introTitle: {
                    defaultValue: {
                        defaultValue: '',
                        overrides: [],
                    },
                    overrides: [],
                },
                contentText: {
                    defaultValue: {
                        defaultValue: '',
                        overrides: [],
                    },
                    overrides: [],
                },
                isCustomRedirectionButton: false,
                redirectButton: {
                    title: {
                        defaultValue: {
                            defaultValue: '',
                            overrides: [],
                        },
                        overrides: [],
                    },
                    url: '',
                },
            },
            customTestDriveBookingSlots: {
                isEnabled: false,
                bookingPeriodType: undefined,
                fixedPeriods: [],
            },
            ownerId: undefined,
        };
    }, [formattedDuplicatedEventData, t]);

    const onSubmit = useHandleError<CreateEventFormValues>(
        async values => {
            setLoading(true);
            // submitting message
            message.loading({
                content: t('eventDetails:messages.createSubmitting'),
                key: 'primary',
                duration: 0,
            });

            const {
                moduleId,
                publicSalesPerson,
                paymentSetting,
                myInfoSetting,
                dealerVariantsMapping,
                kycPresets,
                customizedFields,
                startTime,
                endTime,
                hasCustomiseEmail,
                eventLevelEmailSettings,
                hasCustomiseBanner,
                isCapEnabled,
                banner,
                excludeVehicleOfInterest,
                moduleLevelMobileVerification,
                ...others
            } = values;

            const updatedCustomizedFields = customizedFields.map(
                fields => ({ ...omit(['index'], fields) }) as CustomizedFieldInput
            );

            // change period to company timezone
            const period = {
                start: dayjs(others.period.start)
                    .hour(startTime.hour())
                    .minute(startTime.minute())
                    .second(0)
                    .tz(company?.timeZone, true)
                    .toDate(),
                end: dayjs(others.period.end)
                    .hour(endTime.hour())
                    .minute(endTime.minute())
                    .second(0)
                    .tz(company?.timeZone, true)
                    .toDate(),
            };

            const { introImage, ...submitOrderSetting } = eventLevelEmailSettings.submitOrder;
            const { bannerImage, mobileBannerImage, ...bannerSetting } = banner;

            const transformedCustomTestDriveBookingSlots = transformCustomTestDriveBookingSlotsForCreate(
                values,
                company?.timeZone
            );

            // submit creation
            await apolloClient
                .mutate<CreateEventMutation, CreateEventMutationVariables>({
                    mutation: CreateEventDocument,
                    variables: {
                        moduleId,
                        eventInput: {
                            ...omit(['customTestDriveBookingSlots'], others),
                            ...(!others.privateAccess && { publicSalesPerson }),
                            ...(myInfoSetting?.defaultId && { myInfoSetting }),
                            ...(hasPaymentScenario(others.scenarios) && {
                                paymentSetting: processDealershipPaymentSettingInput(paymentSetting),
                            }),
                            period,
                            dealerVehicles: values.dealerIds.map(dealerId => ({
                                dealerId,
                                vehicleSuiteIds: dealerVariantsMapping[dealerId],
                            })),
                            customizedFields: updatedCustomizedFields,
                            hasCustomiseEmail,
                            ...(hasCustomiseEmail && {
                                eventLevelEmailSettings: {
                                    emailContentUpdateType: EmailContentUpdateType.Module,
                                    submitOrder: {
                                        ...submitOrderSetting,
                                    },
                                    testDrive: {
                                        customer: {
                                            endTestDriveWithProcess: omit(
                                                ['introImage'],
                                                eventLevelEmailSettings.testDrive.customer.endTestDriveWithProcess
                                            ),
                                            submitConfirmation: omit(
                                                ['introImage'],
                                                eventLevelEmailSettings.testDrive.customer.submitConfirmation
                                            ),
                                            bookingConfirmation: omit(
                                                ['introImage'],
                                                eventLevelEmailSettings.testDrive.customer.bookingConfirmation
                                            ),
                                            bookingCancellation: omit(
                                                ['introImage'],
                                                eventLevelEmailSettings.testDrive.customer.bookingCancellation
                                            ),
                                            bookingAmendment: omit(
                                                ['introImage'],
                                                eventLevelEmailSettings.testDrive.customer.bookingAmendment
                                            ),
                                            completeTestDriveWithoutProcess: omit(
                                                ['introImage'],
                                                eventLevelEmailSettings.testDrive.customer
                                                    .completeTestDriveWithoutProcess
                                            ),
                                        },
                                    },
                                },
                            }),
                            hasCustomiseBanner,
                            ...(hasCustomiseBanner && {
                                banner: {
                                    ...bannerSetting,
                                },
                            }),
                            isCapEnabled,
                            hasVehicleIntegration: !excludeVehicleOfInterest,
                            ...(transformedCustomTestDriveBookingSlots !== undefined && {
                                customTestDriveBookingSlots: transformedCustomTestDriveBookingSlots,
                            }),
                        },
                        kycPresets: prepareSubmissionEventKycField({ kycPresets }),
                    },
                })
                .then(async ({ data }) => {
                    if (hasCustomiseEmail) {
                        await handleEventLevelAssets({
                            id: data.event.id,
                            emailAssets: eventLevelEmailSettings,
                            initialValues: data.event.emailContents,
                            forceDownloadAndUpload: !!duplicatedEventData,
                        });
                    }
                    if (hasCustomiseBanner) {
                        await handleBannerAsset({
                            bannerId: data.event.banner.id,
                            values: banner,
                            eventId: data.event.id,
                            forceDownloadAndUpload: !!duplicatedEventData,
                        });
                    }
                })
                .finally(() => {
                    setLoading(false);
                    message.destroy('primary');
                });

            // inform about success
            message.success({
                content: t('eventDetails:messages.createSuccessful'),
                key: 'primary',
            });

            // then go back to list
            navigate(`/admin/events`);
        },
        [
            setLoading,
            t,
            company?.timeZone,
            apolloClient,
            navigate,
            handleEventLevelAssets,
            handleBannerAsset,
            duplicatedEventData,
        ],
        undefined,
        () => setLoading(false)
    );

    const getActiveTab = () => {
        switch (currentTab) {
            case Tab.MainDetails:
                return (
                    <CreateEventForm
                        hasEventToBeDuplicated={!!duplicatedEventData}
                        onCompanyUpdate={setCompany}
                        onFirstEventRouterPathChange={setFirstEventRouterPath}
                    />
                );

            case Tab.CustomerInformation:
                return <EventCustomerInfoTab disabled={false} pageType="Admin" />;

            default:
                throw new Error('Invalid tab');
        }
    };

    return (
        <>
            <ScrollToTop />
            <Formik initialValues={initialValues} onSubmit={onSubmit} validate={validate}>
                {({ handleSubmit }) => (
                    <Form id="eventForm" name="eventForm" onSubmitCapture={handleSubmit}>
                        <FormAutoTouch />
                        <ConsolePageWithHeader
                            footer={[
                                <LoadingButton key="submit" form="eventForm" htmlType="submit" type="primary">
                                    {t('eventDetails:actions.create')}
                                </LoadingButton>,
                            ]}
                            header={{
                                footer: (
                                    <EventDetailsTabs
                                        activeTab={currentTab}
                                        pageType="Admin"
                                        setActiveTab={setCurrentTab}
                                    />
                                ),
                            }}
                            onBack={() => navigate('/admin/events')}
                            title={t('eventDetails:title')}
                        >
                            {getActiveTab()}
                        </ConsolePageWithHeader>
                    </Form>
                )}
            </Formik>
        </>
    );
};

export default AddEventPage;
